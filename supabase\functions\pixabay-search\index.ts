const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { query, page = 1, perPage = 20, imageType = 'all', orientation = 'all' } = await req.json();
    
    if (!query) {
      return new Response(
        JSON.stringify({ error: 'Search query is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const apiKey = Deno.env.get('PIXABAY_API_KEY');
    if (!apiKey) {
      console.error('PIXABAY_API_KEY not found in environment variables');
      return new Response(
        JSON.stringify({ error: 'Pixabay API key not configured' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    console.log(`Searching Pixabay for: "${query}", page: ${page}, orientation: ${orientation}`);

    const searchParams = new URLSearchParams({
      key: apiKey,
      q: query,
      page: page.toString(),
      per_page: perPage.toString(),
      safesearch: 'true',
      image_type: imageType === 'all' ? 'all' : imageType,
      min_width: '640',
      min_height: '480'
    });

    if (orientation && orientation !== 'all') {
      searchParams.append('orientation', orientation);
    }

    const pixabayUrl = `https://pixabay.com/api/?${searchParams.toString()}`;
    console.log('Pixabay API URL:', pixabayUrl);

    const response = await fetch(pixabayUrl);
    
    if (!response.ok) {
      console.error('Pixabay API error:', response.status, response.statusText);
      return new Response(
        JSON.stringify({ error: 'Failed to search images from Pixabay' }),
        { 
          status: response.status, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const data = await response.json();

    const images = data.hits?.map((hit: any) => ({
      id: hit.id.toString(),
      urls: {
        small: hit.webformatURL,
        regular: hit.largeImageURL || hit.webformatURL,
        full: hit.fullHDURL || hit.largeImageURL || hit.webformatURL,
      },
      alt_description: hit.tags || 'Pixabay image',
      user: {
        name: hit.user,
        username: hit.user,
        profile_url: `https://pixabay.com/users/${hit.user}-${hit.user_id}/`,
      },
      download_url: hit.largeImageURL || hit.webformatURL,
      width: hit.imageWidth,
      height: hit.imageHeight,
      tags: hit.tags ? hit.tags.split(', ') : [],
      source_url: hit.pageURL,
      views: hit.views,
      downloads: hit.downloads
    })) || [];

    console.log(`Found ${images.length} images for query: "${query}"`);

    return new Response(
      JSON.stringify({
        images,
        total: data.total || 0,
        totalPages: Math.ceil((data.total || 0) / perPage),
        currentPage: page,
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error in pixabay-search function:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});