-- Create user_details table for storing user profile information
CREATE TABLE public.user_details (
  id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT,
  surname TEXT,
  email TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  PRIMARY KEY (id)
);

-- Enable Row Level Security
ALTER TABLE public.user_details ENABLE ROW LEVEL SECURITY;

-- Create policies for user_details
CREATE POLICY "Users can view their own details" 
ON public.user_details 
FOR SELECT 
USING (auth.uid() = id);

CREATE POLICY "Users can update their own details" 
ON public.user_details 
FOR UPDATE 
USING (auth.uid() = id);

-- <PERSON>reate function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate trigger for automatic timestamp updates
CREATE TRIGGER update_user_details_updated_at
  BEFORE UPDATE ON public.user_details
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Create function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.user_details (id, name, surname, email)
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data ->> 'name',
    NEW.raw_user_meta_data ->> 'surname', 
    NEW.email
  );
  RETURN NEW;
END;
$$;

-- Create trigger to automatically create user_details when user signs up
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW 
  EXECUTE FUNCTION public.handle_new_user();