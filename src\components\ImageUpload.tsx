import { useState, useCallback } from "react";
import { Upload, Image as ImageIcon, <PERSON>rkles, FileImage, Camera } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { StockImageBrowser } from "./StockImageBrowser";
import { AIImageGenerator } from "./AIImageGenerator";

interface ImageUploadProps {
  onImageUpload: (imageUrl: string, file?: File, imageData?: {
    provider: string;
    authorName?: string;
    authorUsername?: string;
    authorProfileUrl?: string;
    imageSourceId?: string;
    imageSourceUrl?: string;
    imageSourceTags?: string[];
    imageBucketPath?: string;
    downloadedAt?: string;
  }) => void;
  onAIGenerate?: () => void;
}

export const ImageUpload = ({ onImageUpload, onAIGenerate }: ImageUploadProps) => {
  const [isDragging, setIsDragging] = useState(false);
  const [showStockBrowser, setShowStockBrowser] = useState(false);
  const [showAIGenerator, setShowAIGenerator] = useState(false);

  const handleFileUpload = useCallback(
    (file: File) => {
      if (!file.type.startsWith("image/")) {
        toast.error("Please upload an image file");
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          onImageUpload(e.target.result as string, file);
          toast.success("Image uploaded successfully!");
        }
      };
      reader.readAsDataURL(file);
    },
    [onImageUpload]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragging(false);
      
      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleFileUpload(files[0]);
      }
    },
    [handleFileUpload]
  );

  const handleFileInput = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);
      if (files.length > 0) {
        handleFileUpload(files[0]);
      }
    },
    [handleFileUpload]
  );

  return (
    <div className="space-y-4">
      {/* Upload from File Option */}
      <Card>
        <CardContent className="p-6">
          <div
            className={`
              relative border-2 border-dashed rounded-lg p-6 text-center transition-all duration-300 cursor-pointer
              ${
                isDragging
                  ? "border-primary bg-primary/10 scale-105"
                  : "border-border hover:border-primary/50 hover:bg-primary/5"
              }
            `}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => document.getElementById("file-input")?.click()}
          >
            <input
              id="file-input"
              type="file"
              accept="image/*"
              onChange={handleFileInput}
              className="hidden"
            />
            
            <div className="space-y-3">
              <div className="flex justify-center">
                {isDragging ? (
                  <ImageIcon className="h-10 w-10 text-primary animate-bounce" />
                ) : (
                  <FileImage className="h-10 w-10 text-muted-foreground" />
                )}
              </div>
              
              <div className="space-y-1">
                <h3 className="font-semibold">
                  {isDragging ? "Drop your image here" : "Upload from File"}
                </h3>
                <p className="text-muted-foreground text-sm">
                  Drag and drop an image file or click to browse
                </p>
                <p className="text-xs text-muted-foreground">
                  Supports JPG, PNG, GIF up to 10MB
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Separator */}
      <div className="relative">
        <Separator />
        <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-background px-2">
          <span className="text-xs text-muted-foreground">OR</span>
        </div>
      </div>

      {/* Stock Images Option */}
      <Card>
        <CardContent className="p-6">
          <div className="text-center space-y-3">
            <div className="flex justify-center">
              <div className="p-3 rounded-full bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-200/20">
                <Camera className="h-8 w-8 text-blue-600" />
              </div>
            </div>
            
            <div className="space-y-1">
              <h3 className="font-semibold">Browse Stock Images</h3>
              <p className="text-muted-foreground text-sm">
                Search millions of free high-quality photos from Unsplash
              </p>
            </div>
            
            <Button 
              onClick={() => setShowStockBrowser(true)}
              className="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white"
            >
              <Camera className="h-4 w-4 mr-2" />
              Browse Stock Images
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Separator */}
      <div className="relative">
        <Separator />
        <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-background px-2">
          <span className="text-xs text-muted-foreground">OR</span>
        </div>
      </div>

      {/* AI Generation Option */}
      <Card>
        <CardContent className="p-6">
          <div className="text-center space-y-3">
            <div className="flex justify-center">
              <div className="p-3 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-200/20">
                <Sparkles className="h-8 w-8 text-purple-600" />
              </div>
            </div>
            
            <div className="space-y-1">
              <h3 className="font-semibold">Generate with AI</h3>
              <p className="text-muted-foreground text-sm">
                Create unique images from text descriptions using AI
              </p>
            </div>
            
            <Button 
              onClick={() => setShowAIGenerator(true)}
              className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
            >
              <Sparkles className="h-4 w-4 mr-2" />
              Generate Image
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Stock Image Browser Modal */}
      {showStockBrowser && (
        <StockImageBrowser
          onImageSelect={(imageUrl, unsplashData) => {
            onImageUpload(imageUrl, undefined, unsplashData);
            toast.success("Stock image selected!");
          }}
          onClose={() => setShowStockBrowser(false)}
        />
      )}

      {/* AI Image Generator Modal */}
      {showAIGenerator && (
        <AIImageGenerator
          onImageGenerated={(imageUrl, metadata) => {
            onImageUpload(imageUrl, undefined, {
              provider: metadata.provider,
              authorName: undefined,
              authorUsername: undefined,
              authorProfileUrl: undefined,
              imageSourceId: metadata.prompt,
              imageSourceUrl: undefined,
              imageSourceTags: [metadata.model, metadata.style, metadata.quality],
              imageBucketPath: undefined,
              downloadedAt: metadata.generatedAt
            });
            toast.success("AI image generated successfully!");
          }}
          onClose={() => setShowAIGenerator(false)}
        />
      )}
    </div>
  );
};