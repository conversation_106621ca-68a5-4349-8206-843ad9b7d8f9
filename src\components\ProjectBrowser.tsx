import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useCampaigns } from '@/hooks/useCampaigns';
import { FolderOpen, Clock, Image, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { useCampaignParams } from '@/utils/urlParams';

interface ProjectBrowserProps {
  onProjectLoad: (project: any) => void;
}

export const ProjectBrowser = ({ onProjectLoad }: ProjectBrowserProps) => {
  const [projects, setProjects] = useState<any[]>([]);
  const { getUserProjects, loadProject, deleteProject, loading } = useCampaigns();
  const campaignParams = useCampaignParams();

  // Use URL parameters for modal state
  const isOpen = campaignParams.modal === 'project-browser';

  const fetchProjects = async () => {
    const userProjects = await getUserProjects();
    setProjects(userProjects);
  };

  useEffect(() => {
    if (isOpen) {
      fetchProjects();
    }
  }, [isOpen]);

  const handleLoadProject = async (projectId: string) => {
    try {
      const project = await loadProject(projectId);
      if (project) {
        onProjectLoad(project);
        campaignParams.clearModal();
        toast.success('Project loaded successfully!');
      }
    } catch (error) {
      toast.error('Failed to load project');
    }
  };

  const handleDeleteProject = async (projectId: string, projectTitle: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent card click when delete button is clicked
    
    const success = await deleteProject(projectId);
    if (success) {
      // Refresh the projects list
      fetchProjects();
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => open ? campaignParams.setModal('project-browser') : campaignParams.clearModal()}>
      <DialogTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2">
          <FolderOpen className="h-4 w-4" />
          Change Project
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Load Existing Project</DialogTitle>
          <DialogDescription>
            Select a project to load into the text editor
          </DialogDescription>
        </DialogHeader>
        
        <div className="overflow-y-auto max-h-[60vh] pr-4">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : projects.length === 0 ? (
            <div className="text-center py-12">
              <Image className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">No projects found</p>
              <p className="text-sm text-muted-foreground mt-2">
                Create your first project by adding text to an image and saving it
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {projects.map((project) => (
                <Card 
                  key={project.id} 
                  className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105 group relative"
                  onClick={() => handleLoadProject(project.id)}
                >
                  {/* Delete Button */}
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Project</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete "{project.title}"? This action cannot be undone and will also remove the associated image from storage.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction 
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          onClick={(e) => handleDeleteProject(project.id, project.title, e)}
                        >
                          Delete Project
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>

                  <CardHeader className="pb-2">
                    <div className="aspect-video w-full bg-muted rounded-md overflow-hidden mb-3">
                      {project.image_url ? (
                        <img 
                          src={project.image_url} 
                          alt={project.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Image className="h-8 w-8 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                    <CardTitle className="text-lg truncate">{project.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      {formatDate(project.updated_at)}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
        
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => campaignParams.clearModal()}>
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};