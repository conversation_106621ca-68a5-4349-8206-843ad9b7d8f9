import { useState, useCallback, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { ImageUpload } from "@/components/ImageUpload";
import { ImageToolbar } from "@/components/ImageToolbar";
import { TextEditor, TextOptions } from "@/components/TextEditor";
import { CanvasEditor } from "@/components/CanvasEditor";
import { ProjectBrowser } from "@/components/ProjectBrowser";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/contexts/AuthContext";
import { useCampaigns, TextOverlayProject } from "@/hooks/useCampaigns";
import { toast } from "sonner";
import DashboardHeader from "@/components/dashboard/DashboardHeader";
import NotebookHeader from "@/components/dashboard/NotebookHeader";
import { useCampaignParams } from "@/utils/urlParams";
import { TextItemsList } from "@/components/TextItemsList";

interface TextItem {
  id: string;
  text: string;
  fontSize: number;
  fontFamily: string;
  color: string;
  visible?: boolean;
  left?: number;
  top?: number;
  fontWeight?: string;
  fontStyle?: string;
  textDecoration?: string;
  stroke?: string;
  strokeWidth?: number;
  shadow?: {
    blur: number;
    offsetX: number;
    offsetY: number;
    color: string;
  };
}

const ImageGenerator = () => {
  const [imageUrl, setImageUrl] = useState<string>("");
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [projectTitle, setProjectTitle] = useState<string>("Untitled Project");
  const [currentProjectId, setCurrentProjectId] = useState<string | null>(null);
  const [addTextToCanvas, setAddTextToCanvas] = useState<
    ((textOptions: TextOptions) => void) | null
  >(null);
  const [updateTextOnCanvas, setUpdateTextOnCanvas] = useState<
    ((textOptions: TextOptions) => void) | null
  >(null);
  const [deleteTextFromCanvas, setDeleteTextFromCanvas] = useState<
    (() => void) | null
  >(null);
  const [getCanvasData, setGetCanvasData] = useState<(() => any) | null>(null);
  const [selectedTextOptions, setSelectedTextOptions] =
    useState<TextOptions | null>(null);
  const [textItems, setTextItems] = useState<TextItem[]>([]);
  const [selectedTextItemId, setSelectedTextItemId] = useState<string | null>(
    null
  );
  const [currentProject, setCurrentProject] = useState<Record<string, any>>({});
  const { user, loading, signOut } = useAuth();
  const { saveProject, isSaving, useLoadProject } = useCampaigns();
  const navigate = useNavigate();
  const location = useLocation();

  // Use URL parameters for navigation state
  const campaignParams = useCampaignParams();

  // Track if we're currently loading a project to prevent loops
  const isLoadingFromUrl = useRef(false);

  // Use React Query to load project data
  const { data: loadedProject, isLoading: isLoadingProject } = useLoadProject(
    campaignParams.mode === "existing" ? campaignParams.projectId : null
  );

  // Update text items from project data (when loading a project)
  const updateTextItemsFromProjectData = useCallback((projectData: TextOverlayProject) => {
    if (projectData && projectData.text_objects && Array.isArray(projectData.text_objects)) {
      const newTextItems: TextItem[] = projectData.text_objects.map(
        (obj: any, index: number) => ({
          id: `text-${index}-${Date.now()}`,
          text: obj.text || "",
          fontSize: obj.fontSize || 32,
          fontFamily: obj.fontFamily || "Arial",
          color: obj.fill || "#000000",
          x: obj.left || 0,
          y: obj.top || 0,
          width: obj.width || 200,
          height: obj.height || 50,
          rotation: obj.angle || 0,
          fontWeight: obj.fontWeight || "normal",
          fontStyle: obj.fontStyle || "normal",
          textAlign: obj.textAlign || "left",
          textDecoration: obj.textDecoration || "none",
        })
      );
      setTextItems(newTextItems);
      console.log('Updated text items from project data:', newTextItems);
    }
  }, []);

  useEffect(() => {
    if (!loading && !user) {
      navigate("/auth");
    }
  }, [user, loading, navigate]);

  // Handle project initialization from URL parameters
  useEffect(() => {
    const { projectId, mode, projectTitle: urlProjectTitle } = campaignParams;

    // Prevent loading if we're already in the process of loading from URL
    if (isLoadingFromUrl.current) {
      return;
    }

    if (mode === "new" && urlProjectTitle && projectTitle !== urlProjectTitle) {
      setProjectTitle(urlProjectTitle);
      setCurrentProjectId(null);
      setImageUrl("");
      setImageFile(null);
      setCurrentProject({});
      setLoadedProjectData(null);
    }
  }, [
    campaignParams.projectId,
    campaignParams.mode,
    campaignParams.projectTitle,
    currentProjectId,
    projectTitle,
  ]);

  // Handle loaded project data from React Query
  useEffect(() => {
    if (loadedProject && campaignParams.mode === "existing" && campaignParams.projectId) {
      // Prevent loading if we're already in the process of loading from URL
      if (isLoadingFromUrl.current) {
        return;
      }

      // Only load if we haven't already loaded this project
      if (currentProjectId !== loadedProject.id) {
        isLoadingFromUrl.current = true;

        setImageUrl(loadedProject.image_url);
        setProjectTitle(loadedProject.title);
        setCurrentProjectId(loadedProject.id);
        setImageFile(null);
        setCurrentProject({
          provider: loadedProject.provider || "upload",
          author_name: loadedProject.author_name,
          author_username: loadedProject.author_username,
          author_profile_url: loadedProject.author_profile_url,
          image_source_id: loadedProject.image_source_id,
          image_source_url: loadedProject.image_source_url,
          image_source_tags: loadedProject.image_source_tags,
          image_bucket_path: loadedProject.image_bucket_path,
          downloaded_at: loadedProject.downloaded_at,
        });
        setLoadedProjectData(loadedProject);
        console.log('ImageGenerator: Set loadedProjectData from React Query:', loadedProject);

        // Update text items list from loaded project data
        updateTextItemsFromProjectData(loadedProject);

        // Prevent canvas updates for a short period to avoid race condition
        setTimeout(() => {
          isLoadingFromUrl.current = false;
        }, 2000);
      }
    }
  }, [loadedProject, campaignParams.mode, campaignParams.projectId, currentProjectId, updateTextItemsFromProjectData]);

  const handleSetAddTextToCanvas = useCallback(
    (fn: (textOptions: TextOptions) => void) => {
      setAddTextToCanvas(() => fn);
    },
    []
  );

  const handleSetUpdateTextOnCanvas = useCallback(
    (fn: (textOptions: TextOptions) => void) => {
      setUpdateTextOnCanvas(() => fn);
    },
    []
  );

  const handleSetDeleteTextFromCanvas = useCallback((fn: () => void) => {
    setDeleteTextFromCanvas(() => fn);
  }, []);

  const handleTextSelected = useCallback((textOptions: TextOptions | null) => {
    setSelectedTextOptions(textOptions);
  }, []);

  // Text items management handlers
  const handleSelectTextItem = useCallback(
    (id: string) => {
      setSelectedTextItemId(id);
      // Find the text item and set it as selected in the editor
      const item = textItems.find((item) => item.id === id);
      if (item) {
        const textOptions: TextOptions = {
          text: item.text,
          fontFamily: item.fontFamily,
          fontSize: item.fontSize,
          fontWeight: item.fontWeight || "normal",
          fontStyle: item.fontStyle || "normal",
          textDecoration: item.textDecoration || "normal",
          fill: item.color,
          stroke: item.stroke,
          strokeWidth: item.strokeWidth,
          shadow: item.shadow,
        };
        setSelectedTextOptions(textOptions);
      }
    },
    [textItems]
  );

  const handleToggleTextItemVisibility = useCallback((id: string) => {
    setTextItems((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, visible: !item.visible } : item
      )
    );
  }, []);

  const handleDeleteTextItem = useCallback(
    (id: string) => {
      setTextItems((prev) => prev.filter((item) => item.id !== id));
      if (selectedTextItemId === id) {
        setSelectedTextItemId(null);
        setSelectedTextOptions(null);
      }
      // Also delete from canvas if deleteTextFromCanvas is available
      if (deleteTextFromCanvas) {
        deleteTextFromCanvas();
      }
    },
    [selectedTextItemId, deleteTextFromCanvas]
  );



  // Update text items when canvas changes
  const updateTextItemsFromCanvas = useCallback(() => {
    if (getCanvasData && !isLoadingFromUrl.current) {
      const canvasData = getCanvasData();
      if (canvasData && canvasData.textObjects && canvasData.textObjects.length > 0) {
        const newTextItems: TextItem[] = canvasData.textObjects.map(
          (obj: any, index: number) => ({
            id: `text-${index}-${Date.now()}`,
            text: obj.text || "",
            fontSize: obj.fontSize || 32,
            fontFamily: obj.fontFamily || "Arial",
            color: obj.fill || "#000000",
            visible: true,
            left: obj.left,
            top: obj.top,
            fontWeight: obj.fontWeight,
            fontStyle: obj.fontStyle,
            textDecoration: obj.textDecoration,
            stroke: obj.stroke,
            strokeWidth: obj.strokeWidth,
            shadow: obj.shadow,
          })
        );
        setTextItems(newTextItems);
      }
      // Don't clear text items if canvas is empty - keep existing items
    }
  }, [getCanvasData]);

  const handleImageUpload = useCallback(
    (
      url: string,
      file?: File,
      imageData?: {
        provider: string;
        authorName?: string;
        authorUsername?: string;
        authorProfileUrl?: string;
        imageSourceId?: string;
        imageSourceUrl?: string;
        imageSourceTags?: string[];
        imageBucketPath?: string;
        downloadedAt?: string;
      }
    ) => {
      setImageUrl(url);
      setImageFile(file || null);

      // Store image data for saving
      if (imageData) {
        setCurrentProject((prev) => ({
          ...prev,
          provider: imageData.provider,
          author_name: imageData.authorName,
          author_username: imageData.authorUsername,
          author_profile_url: imageData.authorProfileUrl,
          image_source_id: imageData.imageSourceId,
          image_source_url: imageData.imageSourceUrl,
          image_source_tags: imageData.imageSourceTags,
          image_bucket_path: imageData.imageBucketPath,
          downloaded_at: imageData.downloadedAt,
        }));
      } else {
        // Reset for uploaded files
        setCurrentProject((prev) => ({
          ...prev,
          provider: "upload",
          author_name: undefined,
          author_username: undefined,
          author_profile_url: undefined,
          image_source_id: undefined,
          image_source_url: undefined,
          image_source_tags: undefined,
          image_bucket_path: undefined,
          downloaded_at: undefined,
        }));
      }
    },
    []
  );

  const handleAddText = useCallback(
    (textOptions: TextOptions) => {
      if (addTextToCanvas && textOptions) {
        addTextToCanvas(textOptions);
      }
    },
    [addTextToCanvas]
  );

  const handleUpdateText = useCallback(
    (textOptions: TextOptions) => {
      if (updateTextOnCanvas && textOptions) {
        updateTextOnCanvas(textOptions);
      }
    },
    [updateTextOnCanvas]
  );

  const handleDeleteText = useCallback(() => {
    if (deleteTextFromCanvas) {
      deleteTextFromCanvas();
    }
  }, [deleteTextFromCanvas]);

  const handleSetGetCanvasData = useCallback((fn: () => any) => {
    setGetCanvasData(() => fn);
  }, []);

  const handleSaveProject = useCallback(async () => {
    if (!getCanvasData) {
      toast.error("Canvas not ready");
      return;
    }

    const canvasData = getCanvasData();

    const project = {
      id: currentProjectId,
      title: projectTitle,
      image_url: imageUrl,
      image_bucket_path:
        currentProject.image_bucket_path ||
        (currentProject.provider === "pixabay"
          ? currentProject.image_bucket_path
          : imageFile
          ? ""
          : ""),
      text_objects: canvasData.textObjects,
      canvas_settings: canvasData.canvasSettings,
      provider: currentProject.provider || "upload",
      author_name: currentProject.author_name,
      author_username: currentProject.author_username,
      author_profile_url: currentProject.author_profile_url,
      image_source_id: currentProject.image_source_id,
      image_source_url: currentProject.image_source_url,
      image_source_tags: currentProject.image_source_tags,
      downloaded_at: currentProject.downloaded_at,
    };

    try {
      const savedId = await saveProject({ project, imageFile: imageFile || undefined });
      if (savedId && !currentProjectId) {
        setCurrentProjectId(savedId);
      }
    } catch (error) {
      console.error('Failed to save project:', error);
    }
  }, [
    getCanvasData,
    currentProjectId,
    projectTitle,
    imageUrl,
    imageFile,
    saveProject,
    currentProject,
  ]);

  const handleProjectLoad = useCallback(
    async (project: TextOverlayProject) => {
      // Set flag to prevent useEffect from triggering
      isLoadingFromUrl.current = true;

      // Set the project data first
      setImageUrl(project.image_url);
      setProjectTitle(project.title);
      setCurrentProjectId(project.id);
      setImageFile(null); // Clear file since we're loading from URL

      // Set project metadata including all audit data
      setCurrentProject({
        provider: project.provider || "upload",
        author_name: project.author_name,
        author_username: project.author_username,
        author_profile_url: project.author_profile_url,
        image_source_id: project.image_source_id,
        image_source_url: project.image_source_url,
        image_source_tags: project.image_source_tags,
        image_bucket_path: project.image_bucket_path,
        downloaded_at: project.downloaded_at,
      });

      // Store project data to be loaded into canvas
      setLoadedProjectData(project);
      console.log('ImageGenerator: Set loadedProjectData from handleProjectLoad:', project);

      // Update text items list from loaded project data
      updateTextItemsFromProjectData(project);

      // Update URL parameters after setting the state to prevent loops
      campaignParams.setMultiple({
        projectId: project.id,
        mode: "existing",
        projectTitle: project.title,
      });

      // Reset flag after a longer delay to prevent race condition
      setTimeout(() => {
        isLoadingFromUrl.current = false;
      }, 2000);
    },
    [campaignParams, updateTextItemsFromProjectData]
  );

  // Add state for loaded project data
  const [loadedProjectData, setLoadedProjectData] = useState<TextOverlayProject | null>(null);

  // Update text items when canvas data changes
  useEffect(() => {
    const interval = setInterval(() => {
      updateTextItemsFromCanvas();
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, [updateTextItemsFromCanvas]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-muted-foreground">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <NotebookHeader title={projectTitle} notebookId={currentProjectId} />
      <header className="border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 lg:gap-4 mt-2 flex-wrap">
                <Input
                  value={projectTitle}
                  onChange={(e) => setProjectTitle(e.target.value)}
                  className="max-w-[200px] lg:max-w-xs"
                  placeholder="Project title"
                />
                <ProjectBrowser onProjectLoad={handleProjectLoad} />
                {imageUrl && (
                  <Button
                    onClick={handleSaveProject}
                    disabled={isSaving}
                    className="bg-primary hover:bg-primary/90"
                    size="sm"
                  >
                    {isSaving ? "Saving..." : "Save"}
                  </Button>
                )}
              </div>
            </div>

            {/* Image Toolbar - Always visible on tablets and larger */}
            {/* <div className="hidden md:block">
              <ImageToolbar onImageUpload={handleImageUpload} compact />
            </div> */}
          </div>

          {/* Mobile Image Toolbar - Below header on small screens */}
          {/* <div className="md:hidden mt-4 pt-4 border-t border-border">
            <ImageToolbar onImageUpload={handleImageUpload} compact />
          </div> */}
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 xl:grid-cols-4 lg:grid-cols-3 gap-4">
          {/* Left Sidebar - Tools */}
          <div className="xl:col-span-1 lg:col-span-1 space-y-4">
            {imageUrl && (
              <div className="animate-slide-in space-y-3">
                {/* Text Items List - Compact on smaller screens */}
                <div className="max-h-[40vh] lg:max-h-[50vh] overflow-y-auto">
                  <TextItemsList
                    textItems={textItems}
                    selectedItemId={selectedTextItemId}
                    onSelectItem={handleSelectTextItem}
                    onToggleVisibility={handleToggleTextItemVisibility}
                    onDeleteItem={handleDeleteTextItem}
                  />
                </div>

                {/* Text Properties Editor - Scrollable on smaller screens */}
                <div className="max-h-[50vh] lg:max-h-[60vh] overflow-y-auto">
                  <TextEditor
                    onAddText={handleAddText}
                    onUpdateText={handleUpdateText}
                    onDeleteText={handleDeleteText}
                    selectedTextOptions={selectedTextOptions}
                  />
                </div>
              </div>
            )}
            {/* Always show ImageToolbar in sidebar on desktop for easy image changes */}
            <div className="hidden lg:block animate-fade-in">
              <ImageToolbar onImageUpload={handleImageUpload} />
            </div>

            {!imageUrl && (
              <div className="lg:hidden animate-fade-in">
                <ImageUpload onImageUpload={handleImageUpload} />
              </div>
            )}
          </div>

          {/* Main Canvas Area */}
          <div className="xl:col-span-3 lg:col-span-2 space-y-4">
            <div className="animate-fade-in">
              <CanvasEditor
                imageUrl={imageUrl}
                onAddText={handleSetAddTextToCanvas}
                onUpdateText={handleSetUpdateTextOnCanvas}
                onDeleteText={handleSetDeleteTextFromCanvas}
                onTextSelected={handleTextSelected}
                onGetCanvasData={handleSetGetCanvasData}
                loadedProjectData={loadedProjectData}
                onProjectDataLoaded={() => {
                  console.log('ImageGenerator: Canvas finished loading project data, clearing loadedProjectData');
                  setLoadedProjectData(null);
                }}
              />
            </div>

            {/* Unsplash Attribution */}
            {currentProject.provider === "unsplash" &&
              currentProject.author_name && (
                <div className="text-xs text-muted-foreground bg-card/50 p-3 rounded-lg border">
                  Photo by{" "}
                  <a
                    href={currentProject.author_profile_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline hover:text-foreground"
                  >
                    {currentProject.author_name}
                  </a>{" "}
                  on{" "}
                  <a
                    href="https://unsplash.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline hover:text-foreground"
                  >
                    Unsplash
                  </a>
                </div>
              )}
          </div>
        </div>

        {/* Instructions */}
        {!imageUrl && (
          <div className="mt-12 text-center space-y-4 animate-fade-in">
            <h2 className="text-xl font-semibold text-foreground">
              How to use Text Overlay Editor
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <div className="space-y-2">
                <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto text-primary-foreground font-bold">
                  1
                </div>
                <h3 className="font-semibold">Upload Image</h3>
                <p className="text-muted-foreground text-sm">
                  Drag and drop or click to upload your image
                </p>
              </div>
              <div className="space-y-2">
                <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto text-primary-foreground font-bold">
                  2
                </div>
                <h3 className="font-semibold">Customize Text</h3>
                <p className="text-muted-foreground text-sm">
                  Add text with custom fonts, colors, and effects
                </p>
              </div>
              <div className="space-y-2">
                <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto text-primary-foreground font-bold">
                  3
                </div>
                <h3 className="font-semibold">Export</h3>
                <p className="text-muted-foreground text-sm">
                  Download your image with text overlays
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageGenerator;
