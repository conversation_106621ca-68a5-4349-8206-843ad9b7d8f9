-- Enable RLS on auth.users to ensure proper access control
-- Note: We're not creating a profiles table since this is login-only
-- If profile data is needed later, we can add it

-- Create a simple table to track login attempts for security
CREATE TABLE public.login_attempts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  attempted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  success BOOLEAN NOT NULL DEFAULT false,
  ip_address INET
);

-- Enable RLS
ALTER TABLE public.login_attempts ENABLE ROW LEVEL SECURITY;

-- Users can only view their own login attempts
CREATE POLICY "Users can view their own login attempts" 
ON public.login_attempts 
FOR SELECT 
USING (auth.uid() = user_id);

-- System can insert login attempts
CREATE POLICY "System can insert login attempts" 
ON public.login_attempts 
FOR INSERT 
WITH CHECK (true);