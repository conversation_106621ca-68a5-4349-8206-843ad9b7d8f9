import { createApi } from 'https://esm.sh/unsplash-js@7.0.19'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { query, page = 1, perPage = 20, orientation } = await req.json();
    
    if (!query) {
      return new Response(
        JSON.stringify({ error: 'Search query is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const accessKey = Deno.env.get('UNSPLASH_ACCESS_KEY');
    if (!accessKey) {
      console.error('UNSPLASH_ACCESS_KEY not found in environment variables');
      return new Response(
        JSON.stringify({ error: 'Unsplash API key not configured' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const unsplash = createApi({
      accessKey,
    });

    console.log(`Searching Unsplash for: "${query}", page: ${page}, orientation: ${orientation}`);

    const searchParams: any = {
      query,
      page,
      perPage,
    };

    if (orientation && orientation !== 'all') {
      searchParams.orientation = orientation;
    }

    const result = await unsplash.search.getPhotos(searchParams);

    if (result.errors) {
      console.error('Unsplash API errors:', result.errors);
      return new Response(
        JSON.stringify({ error: 'Failed to search images', details: result.errors }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const response = result.response;
    const images = response?.results?.map(photo => ({
      id: photo.id,
      urls: {
        small: photo.urls.small,
        regular: photo.urls.regular,
        full: photo.urls.full,
      },
      alt_description: photo.alt_description || photo.description || 'Unsplash image',
      user: {
        name: photo.user.name,
        username: photo.user.username,
        profile_url: `https://unsplash.com/@${photo.user.username}`,
      },
      download_url: photo.links.download,
      width: photo.width,
      height: photo.height,
    })) || [];

    console.log(`Found ${images.length} images for query: "${query}"`);

    return new Response(
      JSON.stringify({
        images,
        total: response?.total || 0,
        totalPages: response?.total_pages || 0,
        currentPage: page,
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error in unsplash-search function:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});