import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Type, Eye, EyeOff, Trash2, MapPin, Palette, Underline } from 'lucide-react';

interface TextItem {
  id: string;
  text: string;
  fontSize: number;
  fontFamily: string;
  color: string;
  visible?: boolean;
  left?: number;
  top?: number;
  fontWeight?: string;
  fontStyle?: string;
  textDecoration?: string;
  stroke?: string;
  strokeWidth?: number;
  shadow?: {
    blur: number;
    offsetX: number;
    offsetY: number;
    color: string;
  };
}

interface TextItemsListProps {
  textItems: TextItem[];
  selectedItemId?: string;
  onSelectItem: (id: string) => void;
  onToggleVisibility: (id: string) => void;
  onDeleteItem: (id: string) => void;
}

export const TextItemsList: React.FC<TextItemsListProps> = ({
  textItems,
  selectedItemId,
  onSelectItem,
  onToggleVisibility,
  onDeleteItem,
}) => {
  const formatPosition = (left?: number, top?: number) => {
    if (left !== undefined && top !== undefined) {
      return `${Math.round(left)}, ${Math.round(top)}`;
    }
    return 'Not positioned';
  };

  const hasEffects = (item: TextItem) => {
    return (item.strokeWidth && item.strokeWidth > 0) || 
           (item.shadow && item.shadow.blur > 0) ||
           (item.fontWeight && item.fontWeight !== 'normal') ||
           (item.fontStyle && item.fontStyle !== 'normal') ||
           (item.textDecoration && item.textDecoration !== 'normal');
  };

  return (
    <Card className="bg-card border-border">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Type className="h-4 w-4" />
          Text Items ({textItems.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {textItems.length === 0 ? (
          <p className="text-sm text-muted-foreground text-center py-4">
            No text items added yet
          </p>
        ) : (
          textItems.map((item) => (
            <div
              key={item.id}
              className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                selectedItemId === item.id
                  ? 'border-primary bg-primary/5'
                  : 'border-border hover:bg-muted/50'
              }`}
              onClick={() => onSelectItem(item.id)}
            >
              <div className="space-y-2">
                {/* Text content and main info */}
                <div className="flex items-center justify-between gap-2">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {item.text || 'Empty text'}
                    </p>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        onToggleVisibility(item.id);
                      }}
                    >
                      {item.visible !== false ? (
                        <Eye className="h-3 w-3" />
                      ) : (
                        <EyeOff className="h-3 w-3" />
                      )}
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteItem(item.id);
                      }}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                {/* Typography info */}
                <div className="flex items-center gap-2 flex-wrap">
                  <Badge variant="secondary" className="text-xs">
                    {item.fontFamily}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {item.fontSize}px
                  </Badge>
                  {hasEffects(item) && (
                    <Badge variant="default" className="text-xs bg-primary/10 text-primary border-primary/20">
                      <Palette className="h-2 w-2 mr-1" />
                      Effects
                    </Badge>
                  )}
                </div>

                {/* Position and color info */}
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    <span>{formatPosition(item.left, item.top)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded border border-border"
                      style={{ backgroundColor: item.color }}
                      title={`Color: ${item.color}`}
                    />
                    {item.strokeWidth && item.strokeWidth > 0 && (
                      <div className="flex items-center gap-1">
                        <Underline className="h-3 w-3" />
                        <span>{item.strokeWidth}px</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Effects details (if any) */}
                {item.shadow && item.shadow.blur > 0 && (
                  <div className="text-xs text-muted-foreground">
                    Shadow: {item.shadow.blur}px blur, {item.shadow.offsetX}px, {item.shadow.offsetY}px
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  );
};
