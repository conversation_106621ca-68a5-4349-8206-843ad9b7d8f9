import { useNavigate, useLocation } from 'react-router-dom';
import { useCallback, useMemo } from 'react';

// Type definitions for URL parameters
export interface CampaignUrlParams {
  projectId?: string;
  mode?: 'new' | 'existing';
  view?: 'canvas' | 'settings';
  modal?: 'stock-browser' | 'ai-generator' | 'project-browser';
  stockTab?: 'pixabay' | 'unsplash';
  projectTitle?: string;
}

// Utility functions for URL parameter management
export class UrlParamsManager {
  private searchParams: URLSearchParams;
  private navigate: (url: string, options?: { replace?: boolean }) => void;
  private pathname: string;

  constructor(searchParams: URLSearchParams, navigate: (url: string, options?: { replace?: boolean }) => void, pathname: string) {
    this.searchParams = new URLSearchParams(searchParams);
    this.navigate = navigate;
    this.pathname = pathname;
  }

  // Get a parameter value
  get(key: keyof CampaignUrlParams): string | undefined {
    return this.searchParams.get(key) || undefined;
  }

  // Get all parameters as an object
  getAll(): CampaignUrlParams {
    const params: CampaignUrlParams = {};
    
    const projectId = this.searchParams.get('projectId');
    if (projectId) params.projectId = projectId;
    
    const mode = this.searchParams.get('mode');
    if (mode === 'new' || mode === 'existing') params.mode = mode;
    
    const view = this.searchParams.get('view');
    if (view === 'canvas' || view === 'settings') params.view = view;
    
    const modal = this.searchParams.get('modal');
    if (modal === 'stock-browser' || modal === 'ai-generator' || modal === 'project-browser') {
      params.modal = modal;
    }
    
    const stockTab = this.searchParams.get('stockTab');
    if (stockTab === 'pixabay' || stockTab === 'unsplash') params.stockTab = stockTab;
    
    const projectTitle = this.searchParams.get('projectTitle');
    if (projectTitle) params.projectTitle = projectTitle;
    
    return params;
  }

  // Set a single parameter
  set(key: keyof CampaignUrlParams, value: string | undefined, options?: { replace?: boolean }) {
    if (value === undefined || value === null || value === '') {
      this.searchParams.delete(key);
    } else {
      this.searchParams.set(key, value);
    }
    
    this.updateUrl(options?.replace);
  }

  // Set multiple parameters at once
  setMultiple(params: Partial<CampaignUrlParams>, options?: { replace?: boolean }) {
    Object.entries(params).forEach(([key, value]) => {
      if (value === undefined || value === null || value === '') {
        this.searchParams.delete(key);
      } else {
        this.searchParams.set(key, String(value));
      }
    });
    
    this.updateUrl(options?.replace);
  }

  // Remove a parameter
  remove(key: keyof CampaignUrlParams, options?: { replace?: boolean }) {
    this.searchParams.delete(key);
    this.updateUrl(options?.replace);
  }

  // Remove multiple parameters
  removeMultiple(keys: (keyof CampaignUrlParams)[], options?: { replace?: boolean }) {
    keys.forEach(key => this.searchParams.delete(key));
    this.updateUrl(options?.replace);
  }

  // Clear all parameters
  clear(options?: { replace?: boolean }) {
    this.searchParams = new URLSearchParams();
    this.updateUrl(options?.replace);
  }

  // Check if a parameter exists
  has(key: keyof CampaignUrlParams): boolean {
    return this.searchParams.has(key);
  }

  // Get the current URL with parameters
  getCurrentUrl(): string {
    const params = this.searchParams.toString();
    return params ? `${this.pathname}?${params}` : this.pathname;
  }

  private updateUrl(replace = false) {
    const params = this.searchParams.toString();
    const url = params ? `${this.pathname}?${params}` : this.pathname;
    this.navigate(url, { replace });
  }
}

// React hook for URL parameter management
export const useUrlParams = (): UrlParamsManager => {
  const navigate = useNavigate();
  const location = useLocation();
  
  const navigateWrapper = useCallback((url: string, options?: { replace?: boolean }) => {
    navigate(url, options);
  }, [navigate]);

  return useMemo(() => {
    const searchParams = new URLSearchParams(location.search);
    return new UrlParamsManager(searchParams, navigateWrapper, location.pathname);
  }, [location.search, location.pathname, navigateWrapper]);
};

// Helper hook to get specific parameter values with type safety
export const useCampaignParams = () => {
  const urlParams = useUrlParams();
  
  return useMemo(() => ({
    projectId: urlParams.get('projectId'),
    mode: urlParams.get('mode') as 'new' | 'existing' | undefined,
    view: urlParams.get('view') as 'canvas' | 'settings' | undefined,
    modal: urlParams.get('modal') as 'stock-browser' | 'ai-generator' | 'project-browser' | undefined,
    stockTab: urlParams.get('stockTab') as 'pixabay' | 'unsplash' | undefined,
    projectTitle: urlParams.get('projectTitle'),
    
    // Utility methods
    setProjectId: (id: string | undefined) => urlParams.set('projectId', id),
    setMode: (mode: 'new' | 'existing' | undefined) => urlParams.set('mode', mode),
    setView: (view: 'canvas' | 'settings' | undefined) => urlParams.set('view', view),
    setModal: (modal: 'stock-browser' | 'ai-generator' | 'project-browser' | undefined) => urlParams.set('modal', modal),
    setStockTab: (tab: 'pixabay' | 'unsplash' | undefined) => urlParams.set('stockTab', tab),
    setProjectTitle: (title: string | undefined) => urlParams.set('projectTitle', title),
    
    // Batch operations
    setMultiple: (params: Partial<CampaignUrlParams>) => urlParams.setMultiple(params),
    clearModal: () => urlParams.remove('modal'),
    clearAll: () => urlParams.clear(),
    
    // URL management
    urlParams
  }), [urlParams]);
};

// Helper function to create campaign URLs with parameters
export const createCampaignUrl = (params: CampaignUrlParams): string => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.set(key, String(value));
    }
  });
  
  const queryString = searchParams.toString();
  return queryString ? `/campaign?${queryString}` : '/campaign';
};

// Helper function to parse campaign URL parameters
export const parseCampaignUrl = (url: string): CampaignUrlParams => {
  const urlObj = new URL(url, window.location.origin);
  const searchParams = urlObj.searchParams;
  
  const params: CampaignUrlParams = {};
  
  const projectId = searchParams.get('projectId');
  if (projectId) params.projectId = projectId;
  
  const mode = searchParams.get('mode');
  if (mode === 'new' || mode === 'existing') params.mode = mode;
  
  const view = searchParams.get('view');
  if (view === 'canvas' || view === 'settings') params.view = view;
  
  const modal = searchParams.get('modal');
  if (modal === 'stock-browser' || modal === 'ai-generator' || modal === 'project-browser') {
    params.modal = modal;
  }
  
  const stockTab = searchParams.get('stockTab');
  if (stockTab === 'pixabay' || stockTab === 'unsplash') params.stockTab = stockTab;
  
  const projectTitle = searchParams.get('projectTitle');
  if (projectTitle) params.projectTitle = projectTitle;
  
  return params;
};
