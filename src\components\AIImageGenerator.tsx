import { useState } from "react";
import { <PERSON><PERSON><PERSON>, Wand2, <PERSON><PERSON><PERSON>, Settings } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

interface AIImageGeneratorProps {
  onImageGenerated: (imageUrl: string, metadata: {
    provider: string;
    prompt: string;
    revisedPrompt?: string;
    model: string;
    size: string;
    quality: string;
    style: string;
    generatedAt: string;
  }) => void;
  onClose: () => void;
}

export const AIImageGenerator = ({ onImageGenerated, onClose }: AIImageGeneratorProps) => {
  const [prompt, setPrompt] = useState("");
  const [size, setSize] = useState("1024x1024");
  const [quality, setQuality] = useState("standard");
  const [style, setStyle] = useState("vivid");
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a prompt");
      return;
    }

    if (prompt.length > 1000) {
      toast.error("Prompt must be less than 1000 characters");
      return;
    }

    setIsGenerating(true);

    try {
      const { data, error } = await supabase.functions.invoke('generate-ai-image', {
        body: {
          prompt: prompt.trim(),
          size,
          quality,
          style
        }
      });

      if (error) {
        console.error('Supabase function error:', error);
        toast.error(`Failed to generate image: ${error.message}`);
        return;
      }

      if (data.error) {
        console.error('AI generation error:', data.error);
        toast.error(`Failed to generate image: ${data.details || data.error}`);
        return;
      }

      // Convert base64 to data URL
      const imageUrl = `data:image/png;base64,${data.imageData}`;
      
      const metadata = {
        provider: 'openai-dalle',
        prompt: prompt.trim(),
        revisedPrompt: data.revisedPrompt,
        model: 'dall-e-3',
        size,
        quality,
        style,
        generatedAt: new Date().toISOString()
      };

      onImageGenerated(imageUrl, metadata);
      toast.success("Image generated successfully!");
      onClose();
    } catch (error) {
      console.error('Error generating image:', error);
      toast.error("Failed to generate image. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const promptSuggestions = [
    "A modern minimalist workspace with clean lines",
    "Abstract geometric shapes in vibrant colors",
    "A serene landscape with mountains and lakes",
    "Urban cityscape at golden hour",
    "Vintage botanical illustration style",
    "Futuristic technology concept art"
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-2 sm:p-4 z-50 overflow-y-auto">
      <div className="w-full h-full flex items-center justify-center">
        <Card className="w-full max-w-2xl my-4 mx-2 sm:mx-4 max-h-[calc(100vh-2rem)] overflow-y-auto">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-purple-600" />
              Generate Image with AI
            </CardTitle>
          </CardHeader>
        <CardContent className="space-y-4 sm:space-y-6 pb-6">
          {/* Prompt Input */}
          <div className="space-y-2">
            <Label htmlFor="prompt">Describe the image you want to create</Label>
            <Textarea
              id="prompt"
              placeholder="A professional marketing banner with bold typography and vibrant colors..."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="min-h-[100px] sm:min-h-[120px] resize-none"
              maxLength={1000}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{prompt.length}/1000 characters</span>
              <span className="hidden sm:inline">Be specific for better results</span>
            </div>
          </div>

          {/* Prompt Suggestions */}
          <div className="space-y-2">
            <Label>Quick suggestions:</Label>
            <div className="flex flex-wrap gap-1 sm:gap-2">
              {promptSuggestions.map((suggestion, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => setPrompt(suggestion)}
                  className="text-xs px-2 py-1 h-auto"
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </div>

          {/* Generation Options */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
            <div className="space-y-2">
              <Label htmlFor="size">Size</Label>
              <Select value={size} onValueChange={setSize}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1024x1024">Square (1024×1024)</SelectItem>
                  <SelectItem value="1024x1792">Portrait (1024×1792)</SelectItem>
                  <SelectItem value="1792x1024">Landscape (1792×1024)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="quality">Quality</Label>
              <Select value={quality} onValueChange={setQuality}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="standard">Standard</SelectItem>
                  <SelectItem value="hd">HD (Higher Cost)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="style">Style</Label>
              <Select value={style} onValueChange={setStyle}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="vivid">Vivid (Dramatic)</SelectItem>
                  <SelectItem value="natural">Natural (Realistic)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row justify-end gap-2 sm:gap-3 pt-4">
            <Button 
              variant="outline" 
              onClick={onClose} 
              disabled={isGenerating}
              className="w-full sm:w-auto"
            >
              Cancel
            </Button>
            <Button 
              onClick={handleGenerate} 
              disabled={isGenerating || !prompt.trim()}
              className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
            >
              {isGenerating ? (
                <>
                  <Wand2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Generate Image
                </>
              )}
            </Button>
          </div>

          {/* Info Note */}
          <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded-lg">
            <p className="font-medium mb-1">💡 Tips for better results:</p>
            <ul className="space-y-1">
              <li>• Be specific about style, colors, and composition</li>
              <li>• Include details about lighting and mood</li>
              <li>• Mention specific art styles if desired (e.g., "watercolor", "minimalist")</li>
              <li>• Avoid complex scenes with many subjects</li>
            </ul>
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  );
};