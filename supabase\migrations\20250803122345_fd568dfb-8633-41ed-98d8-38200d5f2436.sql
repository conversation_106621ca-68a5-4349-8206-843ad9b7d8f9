-- Create storage policies for public-images bucket
CREATE POLICY "Allow public read access" 
ON storage.objects 
FOR SELECT 
USING (bucket_id = 'public-images');

CREATE POLICY "Allow authenticated users to upload images" 
ON storage.objects 
FOR INSERT 
WITH CHECK (bucket_id = 'public-images' AND auth.role() = 'authenticated');

CREATE POLICY "Allow users to update their own images" 
ON storage.objects 
FOR UPDATE 
USING (bucket_id = 'public-images' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Allow users to delete their own images" 
ON storage.objects 
FOR DELETE 
USING (bucket_id = 'public-images' AND auth.uid()::text = (storage.foldername(name))[1]);