import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, FileText, Globe, Video, Mic, Image } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useNotebooks } from "@/hooks/useNotebooks";
import { ProjectNewTitleModal } from "../ProjectNewTitleModal";
const EmptyDashboard = () => {
  const navigate = useNavigate();

  const [isProjectModalOpen, setIsProjectModalOpen] = useState(false);
  const { createNotebook, isCreating } = useNotebooks();
  const handleCreateNotebook = () => {
    // console.log("Create notebook button clicked");
    // console.log("isCreating:", isCreating);
    // createNotebook(
    //   {
    //     title: "Untitled notebook",
    //     description: "",
    //   },
    //   {
    //     onSuccess: (data) => {
    //       console.log("Navigating to notebook:", data.id);
    //       navigate(`/notebook/${data.id}`);
    //     },
    //     onError: (error) => {
    //       console.error("Failed to create notebook:", error);
    //     },
    //   }
    // );
    setIsProjectModalOpen(true);
  };
  return (
    <div className="text-center py-16">
      <div className="mb-12">
        <h2 className="text-3xl font-medium text-gray-900 mb-4">
          Create your first campaign
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          CampaignLM is an AI-powered campaign assistant that works
          best with the sources you upload
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto mb-12">
        <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
          <div className="w-12 h-12 bg-orange-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
            <Image className="h-6 w-6 text-orange-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Images</h3>
          <p className="text-gray-600">
            Upload brand assets and visuals for campaign creation
          </p>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
          <div className="w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
            <FileText className="h-6 w-6 text-blue-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">PDFs</h3>
          <p className="text-gray-600">
            Upload brand guidelines, personas and marketing docs
          </p>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
          <div className="w-12 h-12 bg-green-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
            <Globe className="h-6 w-6 text-green-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Websites</h3>
          <p className="text-gray-600">
            Import competitor analysis and market research
          </p>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
          <div className="w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
            <Video className="h-6 w-6 text-purple-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Audio</h3>
          <p className="text-gray-600">
            Add voice notes and audio content for campaigns
          </p>
        </div>
      </div>

      <Button
        onClick={handleCreateNotebook}
        size="lg"
        className="bg-blue-600 hover:bg-blue-700"
        disabled={isCreating}
      >
        <Upload className="h-5 w-5 mr-2" />
        {isCreating ? "Creating..." : "Create campaign"}
      </Button>
       <ProjectNewTitleModal
              isOpen={isProjectModalOpen}
              onClose={() => setIsProjectModalOpen(false)}
            />
    </div>
  );
};
export default EmptyDashboard;
