// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://swsmojqugdqmbnkqqqbm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN3c21vanF1Z2RxbWJua3FxcWJtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzNTM2MjUsImV4cCI6MjA2ODkyOTYyNX0.bCSn11Knxb2UW7fTrmUYNp9pFVffHzYvPqavgsxaLjo";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});