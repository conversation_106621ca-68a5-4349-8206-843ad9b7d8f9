import { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useCampaigns } from "@/hooks/useCampaigns";
import { toast } from "sonner";
import { Plus, FolderOpen, Calendar, Image } from "lucide-react";
import { useEffect } from "react";
import { createCampaignUrl } from "@/utils/urlParams";

interface ProjectSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ProjectSelectionModal = ({ isOpen, onClose }: ProjectSelectionModalProps) => {
  const [step, setStep] = useState<"selection" | "new-project" | "existing-project">("selection");
  const [projectTitle, setProjectTitle] = useState("");
  const [selectedProjectId, setSelectedProjectId] = useState<string>("");
  const [projects, setProjects] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { getUserProjects, loadProject } = useCampaigns();
  const navigate = useNavigate();

  useEffect(() => {
    if (isOpen && step === "existing-project") {
      fetchProjects();
    }
  }, [isOpen, step]);

  const fetchProjects = async () => {
    setIsLoading(true);
    try {
      const userProjects = await getUserProjects();
      setProjects(userProjects);
    } catch (error) {
      toast.error("Failed to load projects");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateNewProject = () => {
    if (!projectTitle.trim()) {
      toast.error("Project title is required");
      return;
    }
    
    // Close modal and navigate to image generator with new project title
    onClose();
    const campaignUrl = createCampaignUrl({
      mode: "new",
      projectTitle: projectTitle.trim()
    });
    navigate(campaignUrl);
  };

  const handleOpenExistingProject = async () => {
    if (!selectedProjectId) {
      toast.error("Please select a project to open");
      return;
    }

    setIsLoading(true);
    try {
      const project = await loadProject(selectedProjectId);
      if (project) {
        onClose();
        const campaignUrl = createCampaignUrl({
          mode: "existing",
          projectId: project.id,
          projectTitle: project.title
        });
        navigate(campaignUrl);
      } else {
        toast.error("Failed to load project");
      }
    } catch (error) {
      toast.error("Failed to load project");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setStep("selection");
    setProjectTitle("");
    setSelectedProjectId("");
    onClose();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        {step === "selection" && (
          <>
            <DialogHeader>
              <DialogTitle>Start Image Generator</DialogTitle>
              <DialogDescription>
                Choose how you'd like to begin working with the Image Generator.
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              <Card 
                className="cursor-pointer hover:bg-accent/50 transition-colors"
                onClick={() => setStep("new-project")}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                      <Plus className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-base">Create New Project</CardTitle>
                      <CardDescription className="text-sm">
                        Start fresh with a new image editing project
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
              </Card>

              <Card 
                className="cursor-pointer hover:bg-accent/50 transition-colors"
                onClick={() => setStep("existing-project")}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-secondary/10 flex items-center justify-center">
                      <FolderOpen className="h-5 w-5 text-secondary-foreground" />
                    </div>
                    <div>
                      <CardTitle className="text-base">Open Existing Project</CardTitle>
                      <CardDescription className="text-sm">
                        Continue working on a previously saved project
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
              </Card>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
            </DialogFooter>
          </>
        )}

        {step === "new-project" && (
          <>
            <DialogHeader>
              <DialogTitle>Create New Project</DialogTitle>
              <DialogDescription>
                Enter a title for your new image editing project.
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="project-title">Project Title</Label>
                <Input
                  id="project-title"
                  value={projectTitle}
                  onChange={(e) => setProjectTitle(e.target.value)}
                  placeholder="Enter project title..."
                  className="w-full"
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setStep("selection")}>
                Back
              </Button>
              <Button onClick={handleCreateNewProject} disabled={!projectTitle.trim()}>
                Create
              </Button>
            </DialogFooter>
          </>
        )}

        {step === "existing-project" && (
          <>
            <DialogHeader>
              <DialogTitle>Open Existing Project</DialogTitle>
              <DialogDescription>
                Select a project to open and continue editing.
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid gap-4 py-4 max-h-[400px] overflow-y-auto">
              {isLoading ? (
                <div className="text-center py-8 text-muted-foreground">
                  Loading projects...
                </div>
              ) : projects.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No projects found. Create your first project to get started!
                </div>
              ) : (
                <RadioGroup value={selectedProjectId} onValueChange={setSelectedProjectId}>
                  {projects.map((project) => (
                    <div key={project.id} className="flex items-center space-x-2">
                      <RadioGroupItem value={project.id} id={project.id} />
                      <Label htmlFor={project.id} className="flex-1 cursor-pointer">
                        <Card className="hover:bg-accent/50 transition-colors">
                          <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                              <div className="w-12 h-12 rounded-lg bg-muted flex items-center justify-center overflow-hidden">
                                {project.image_url ? (
                                  <img 
                                    src={project.image_url} 
                                    alt={project.title}
                                    className="w-full h-full object-cover"
                                  />
                                ) : (
                                  <Image className="h-6 w-6 text-muted-foreground" />
                                )}
                              </div>
                              <div className="flex-1">
                                <h4 className="font-medium">{project.title}</h4>
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <Calendar className="h-3 w-3" />
                                  Updated {formatDate(project.updated_at)}
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setStep("selection")}>
                Back
              </Button>
              <Button 
                onClick={handleOpenExistingProject} 
                disabled={!selectedProjectId || isLoading}
              >
                {isLoading ? "Opening..." : "Open"}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};